<template>
  <div class="vue2-datatable-demo">
    <div class="demo-header">
      <h2>Vue2 DataTable Demo</h2>
      <p>High-performance data table component for Vue 2 with virtual scrolling, advanced search, and CoreUI styling.</p>
    </div>

    <!-- Demo Controls -->
    <div class="demo-controls">
      <div class="control-group">
        <label>
          <input v-model="enableVirtualScrolling" type="checkbox" />
          Enable Virtual Scrolling
        </label>
        <label>
          <input v-model="enablePerformanceMonitoring" type="checkbox" />
          Performance Monitoring
        </label>
        <label>
          <input v-model="showSearch" type="checkbox" />
          Show Search
        </label>
        <label>
          <input v-model="showPagination" type="checkbox" />
          Show Pagination
        </label>
        <label>
          <input v-model="selectOnRowClick" type="checkbox" />
          Select on Row Click
        </label>
        <label>
          <input v-model="multipleSelection" type="checkbox" />
          Multiple Selection
        </label>
      </div>
      
      <div class="control-group">
        <label>
          Theme:
          <select v-model="selectedTheme">
            <option value="default">Default</option>
            <option value="dark">Dark</option>
            <option value="compact">Compact</option>
            <option value="minimal">Minimal</option>
          </select>
        </label>
        
        <label>
          Data Size:
          <select v-model="dataSize" @change="generateData">
            <option value="small">Small (100 rows)</option>
            <option value="medium">Medium (1,000 rows)</option>
            <option value="large">Large (10,000 rows)</option>
            <option value="xlarge">X-Large (50,000 rows)</option>
          </select>
        </label>
      </div>
    </div>

    <!-- Performance Stats -->
    <div v-if="enablePerformanceMonitoring" class="performance-stats">
      <div class="stat-item">
        <span class="stat-label">Render Time:</span>
        <span class="stat-value">{{ performanceStats.renderTime }}ms</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Memory Usage:</span>
        <span class="stat-value">{{ performanceStats.memoryUsage }}MB</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Items Count:</span>
        <span class="stat-value">{{ performanceStats.itemCount }}</span>
      </div>
    </div>

    <!-- Vue2 DataTable -->
    <Vue2DataTable
      :columns="demoColumns"
      :data-source="demoData"
      :theme="selectedTheme"
      :virtual-scroll-enabled="enableVirtualScrolling"
      :enable-performance-monitoring="enablePerformanceMonitoring"
      :show-search="showSearch"
      :show-pagination="showPagination"
      :show-total-bar="true"
      :selectable="true"
      :select-on-row-click="selectOnRowClick"
      :multiple-selection="multipleSelection"
      :striped="true"
      :hover="true"
      :row-height="50"
      :column-width="150"
      search-placeholder="Search demo data..."
      @performance-update="handlePerformanceUpdate"
      @row-click="handleRowClick"
      @row-select="handleRowSelect"
      @select-all="handleSelectAll"
      @selection-change="handleSelectionChange"
      @search="handleSearch"
      @sort="handleSort"
      @page-change="handlePageChange"
      @scroll="handleScroll"
    >
      <!-- Custom slot for status column -->
      <template #status="{ value }">
        <span :class="getStatusClass(value)">{{ value }}</span>
      </template>
      
      <!-- Custom slot for amount column -->
      <template #amount="{ value }">
        <span class="amount-cell">${{ formatNumber(value) }}</span>
      </template>
      
      <!-- Custom slot for progress column -->
      <template #progress="{ value }">
        <div class="progress-bar-demo">
          <div class="progress-fill" :style="{ width: value + '%' }">
            {{ value }}%
          </div>
        </div>
      </template>
    </Vue2DataTable>

    <!-- Event Log -->
    <div class="event-log">
      <h3>Event Log</h3>
      <div class="log-entries">
        <div
          v-for="(event, index) in eventLog"
          :key="index"
          class="log-entry"
        >
          <span class="log-time">{{ event.time }}</span>
          <span class="log-type">{{ event.type }}</span>
          <span class="log-data">{{ event.data }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue2DataTable from './index.js'

export default {
  name: 'Vue2DataTableDemo',
  
  components: {
    Vue2DataTable
  },

  data() {
    return {
      // Demo controls
      enableVirtualScrolling: true,
      enablePerformanceMonitoring: true,
      showSearch: true,
      showPagination: true,
      selectOnRowClick: true,
      multipleSelection: true,
      selectedTheme: 'default',
      dataSize: 'medium',
      
      // Performance stats
      performanceStats: {
        renderTime: 0,
        memoryUsage: 0,
        itemCount: 0
      },
      
      // Event log
      eventLog: [],
      maxLogEntries: 50,
      
      // Demo data
      demoData: [],
      demoColumns: [
        {
          key: 'id',
          label: 'ID',
          sortable: true,
          searchable: true,
          width: '80px',
          type: 'number'
        },
        {
          key: 'name',
          label: 'Name',
          sortable: true,
          searchable: true,
          width: '200px'
        },
        {
          key: 'email',
          label: 'Email',
          sortable: true,
          searchable: true,
          width: '250px'
        },
        {
          key: 'status',
          label: 'Status',
          sortable: true,
          searchable: true,
          width: '120px'
        },
        {
          key: 'amount',
          label: 'Amount',
          sortable: true,
          searchable: false,
          width: '120px',
          type: 'number',
          align: 'right'
        },
        {
          key: 'progress',
          label: 'Progress',
          sortable: true,
          searchable: false,
          width: '150px',
          type: 'number'
        },
        {
          key: 'createdAt',
          label: 'Created',
          sortable: true,
          searchable: true,
          width: '150px',
          type: 'date'
        }
      ]
    }
  },

  created() {
    this.generateData()
  },

  methods: {
    /**
     * Generate demo data
     */
    generateData() {
      const sizes = {
        small: 100,
        medium: 1000,
        large: 10000,
        xlarge: 500000
      }
      
      const count = sizes[this.dataSize] || 1000
      const statuses = ['Active', 'Inactive', 'Pending', 'Completed', 'Cancelled']
      
      this.demoData = Array.from({ length: count }, (_, index) => ({
        id: index + 1,
        name: `User ${index + 1}`,
        email: `user${index + 1}@example.com`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        amount: Math.floor(Math.random() * 10000) + 100,
        progress: Math.floor(Math.random() * 100),
        createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      }))
      
      this.logEvent('data-generated', `Generated ${count} records`)
    },

    /**
     * Handle performance update
     */
    handlePerformanceUpdate(stats) {
      this.performanceStats = stats
    },

    /**
     * Handle row click
     */
    handleRowClick(payload) {
      this.logEvent('row-click', `Clicked row ${payload.index}: ${payload.item.name}`)
    },

    /**
     * Handle row select
     */
    handleRowSelect(payload) {
      this.logEvent('row-select', `${payload.selected ? 'Selected' : 'Deselected'} row: ${payload.item.name}`)
    },

    /**
     * Handle select all
     */
    handleSelectAll(selected) {
      this.logEvent('select-all', `${selected ? 'Selected' : 'Deselected'} all items`)
    },

    /**
     * Handle selection change
     */
    handleSelectionChange(selectedItems) {
      this.logEvent('selection-change', `Selection changed: ${selectedItems.length} items selected`)
    },

    /**
     * Handle search
     */
    handleSearch(payload) {
      this.logEvent('search', `Search: "${payload.searchTerm}" (${payload.results.length} results)`)
    },

    /**
     * Handle sort
     */
    handleSort(payload) {
      this.logEvent('sort', `Sort by ${payload.column} ${payload.direction}`)
    },

    /**
     * Handle page change
     */
    handlePageChange(page) {
      this.logEvent('page-change', `Changed to page ${page}`)
    },

    /**
     * Handle scroll event
     */
    handleScroll(payload) {
      this.logEvent('scroll', `Scroll: ${payload.scrollTop}px, Range: ${payload.startIndex}-${payload.endIndex}, Count: ${payload.visibleCount}`)
    },

    /**
     * Get status CSS class
     */
    getStatusClass(status) {
      const classes = {
        'Active': 'status-active',
        'Inactive': 'status-inactive',
        'Pending': 'status-pending',
        'Completed': 'status-completed',
        'Cancelled': 'status-cancelled'
      }
      return classes[status] || 'status-default'
    },

    /**
     * Format number with commas
     */
    formatNumber(value) {
      return new Intl.NumberFormat().format(value)
    },

    /**
     * Log event
     */
    logEvent(type, data) {
      const event = {
        time: new Date().toLocaleTimeString(),
        type,
        data: typeof data === 'object' ? JSON.stringify(data) : String(data)
      }
      
      this.eventLog.unshift(event)
      
      // Limit log size
      if (this.eventLog.length > this.maxLogEntries) {
        this.eventLog = this.eventLog.slice(0, this.maxLogEntries)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.vue2-datatable-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: 30px;
  text-align: center;
  
  h2 {
    color: #374151;
    margin-bottom: 8px;
  }
  
  p {
    color: #6b7280;
    font-size: 16px;
  }
}

.demo-controls {
  display: flex;
  gap: 30px;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
  
  label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #374151;
    cursor: pointer;
  }
  
  select {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: white;
  }
}

.performance-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bae6fd;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #0369a1;
  font-weight: 500;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
}

/* Custom slot styles */
.status-active {
  background: #d1fae5;
  color: #065f46;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-inactive {
  background: #fee2e2;
  color: #991b1b;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-completed {
  background: #dbeafe;
  color: #1e40af;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.status-cancelled {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.amount-cell {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
  font-weight: 600;
  color: #059669;
}

.progress-bar-demo {
  width: 100%;
  height: 20px;
  background: #e5e7eb;
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 11px;
  font-weight: 600;
  transition: width 0.3s ease;
}

.event-log {
  margin-top: 30px;
  
  h3 {
    color: #374151;
    margin-bottom: 16px;
  }
}

.log-entries {
  max-height: 300px;
  overflow-y: auto;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
  font-size: 13px;
  
  &:last-child {
    border-bottom: none;
  }
}

.log-time {
  color: #6b7280;
  font-family: monospace;
  min-width: 80px;
}

.log-type {
  color: #059669;
  font-weight: 500;
  min-width: 100px;
}

.log-data {
  color: #374151;
  flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .demo-controls {
    flex-direction: column;
    gap: 20px;
  }
  
  .performance-stats {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .log-entry {
    flex-direction: column;
    gap: 4px;
  }
  
  .log-time,
  .log-type {
    min-width: auto;
  }
}
</style>
