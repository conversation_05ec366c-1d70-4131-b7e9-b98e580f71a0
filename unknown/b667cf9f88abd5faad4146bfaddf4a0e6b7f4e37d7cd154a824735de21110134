<template>
  <div class="vue2-datatable-test-demo">
    <div class="demo-header">
      <h1>Vue2DataTable - Virtual Scroll Fixes Demo</h1>
      <p>Testing virtual scrolling with large datasets and enhanced performance</p>
    </div>

    <!-- Controls Panel -->
    <div class="controls-panel">
      <div class="control-group">
        <label>Dataset Size:</label>
        <select v-model="selectedDatasetSize" @change="generateData">
          <option value="1000">1,000 rows</option>
          <option value="5000">5,000 rows</option>
          <option value="10000">10,000 rows</option>
          <option value="50000">50,000 rows</option>
          <option value="100000">100,000 rows</option>
          <option value="500000">500,000 rows</option>
        </select>
      </div>

      <div class="control-group">
        <label>Columns:</label>
        <select v-model="selectedColumnCount" @change="generateColumns">
          <option value="5">5 columns</option>
          <option value="10">10 columns</option>
          <option value="20">20 columns</option>
          <option value="40">40 columns</option>
          <option value="60">60 columns</option>
        </select>
      </div>

      <div class="control-group">
        <label>
          <input type="checkbox" v-model="forceVirtualScrolling">
          Force Virtual Scrolling
        </label>
      </div>

      <div class="control-group">
        <label>
          <input type="checkbox" v-model="enablePerformanceMonitoring">
          Performance Monitoring
        </label>
      </div>

      <div class="control-group">
        <label>
          <input type="checkbox" v-model="enableColumnVirtualization">
          Column Virtualization
        </label>
      </div>
    </div>

    <!-- Performance Stats -->
    <div v-if="enablePerformanceMonitoring" class="performance-stats">
      <div class="stat-item">
        <span class="stat-label">Render Time:</span>
        <span class="stat-value" :class="getRenderTimeClass()">{{ performanceStats.renderTime }}ms</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Memory Usage:</span>
        <span class="stat-value">{{ performanceStats.memoryUsage }}MB</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Items:</span>
        <span class="stat-value">{{ performanceStats.itemCount }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Virtual Scrolling:</span>
        <span class="stat-value" :class="virtualScrollingEnabled ? 'status-active' : 'status-inactive'">
          {{ virtualScrollingEnabled ? 'Active' : 'Inactive' }}
        </span>
      </div>
    </div>

    <!-- Vue2DataTable Component -->
    <Vue2DataTable
      :columns="demoColumns"
      :data-source="demoData"
      :virtual-scroll-enabled="forceVirtualScrolling || null"
      :enable-performance-monitoring="enablePerformanceMonitoring"
      :column-virtualization-enabled="enableColumnVirtualization"
      :selectable="true"
      :show-search="true"
      :show-pagination="!virtualScrollingEnabled"
      :show-total-bar="true"
      :row-height="45"
      :column-width="150"
      :virtual-scroll-threshold="1000"
      search-placeholder="Search in all fields..."
      @performance-update="handlePerformanceUpdate"
      @virtual-scroll-update="handleVirtualScrollUpdate"
      @scroll="handleScroll"
      @selection-change="handleSelectionChange"
    >
      <!-- Custom slot for status column -->
      <template #status="{ value }">
        <span :class="getStatusClass(value)">{{ value }}</span>
      </template>
      
      <!-- Custom slot for amount column -->
      <template #amount="{ value }">
        <span class="amount-cell">${{ formatNumber(value) }}</span>
      </template>

      <!-- Custom slot for date column -->
      <template #created_date="{ value }">
        <span class="date-cell">{{ formatDate(value) }}</span>
      </template>
    </Vue2DataTable>

    <!-- Debug Information -->
    <div v-if="enablePerformanceMonitoring" class="debug-info">
      <h3>Debug Information</h3>
      <div class="debug-grid">
        <div class="debug-section">
          <h4>Virtual Scroll State</h4>
          <div>Start Index: {{ virtualScrollState.startIndex }}</div>
          <div>End Index: {{ virtualScrollState.endIndex }}</div>
          <div>Visible Count: {{ virtualScrollState.visibleCount }}</div>
          <div>Scroll Top: {{ scrollState.scrollTop }}px</div>
        </div>
        
        <div class="debug-section">
          <h4>Performance Metrics</h4>
          <div>Calculation Time: {{ performanceStats.calculationTime }}ms</div>
          <div>Last Update: {{ performanceStats.lastUpdate }}</div>
          <div>Update Count: {{ performanceStats.updateCount }}</div>
        </div>
        
        <div class="debug-section">
          <h4>Selection State</h4>
          <div>Selected Items: {{ selectedItems.length }}</div>
          <div>Total Items: {{ demoData.length }}</div>
          <div>Selection %: {{ getSelectionPercentage() }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue2DataTable from './index.js'

export default {
  name: 'Vue2DataTableTestDemo',
  
  components: {
    Vue2DataTable
  },

  data() {
    return {
      // Configuration
      selectedDatasetSize: 10000,
      selectedColumnCount: 10,
      forceVirtualScrolling: false,
      enablePerformanceMonitoring: true,
      enableColumnVirtualization: false,

      // Data
      demoData: [],
      demoColumns: [],

      // State tracking
      performanceStats: {
        renderTime: 0,
        memoryUsage: 0,
        itemCount: 0,
        calculationTime: 0,
        lastUpdate: null,
        updateCount: 0
      },

      virtualScrollState: {
        startIndex: 0,
        endIndex: 0,
        visibleCount: 0,
        revision: 0
      },

      scrollState: {
        scrollTop: 0,
        scrollLeft: 0,
        direction: 'down'
      },

      selectedItems: []
    }
  },

  computed: {
    virtualScrollingEnabled() {
      return this.forceVirtualScrolling || this.demoData.length > 1000
    }
  },

  mounted() {
    this.generateColumns()
    this.generateData()
  },

  methods: {
    generateColumns() {
      const baseColumns = [
        { key: 'id', label: 'ID', width: '80px', type: 'number', sortable: true },
        { key: 'name', label: 'Name', width: '200px', type: 'text', sortable: true },
        { key: 'email', label: 'Email', width: '250px', type: 'text', sortable: true },
        { key: 'status', label: 'Status', width: '120px', type: 'text', sortable: true },
        { key: 'amount', label: 'Amount', width: '120px', type: 'currency', sortable: true },
        { key: 'created_date', label: 'Created Date', width: '150px', type: 'date', sortable: true }
      ]

      // Add additional columns if needed
      const additionalColumns = []
      for (let i = baseColumns.length; i < this.selectedColumnCount; i++) {
        additionalColumns.push({
          key: `field_${i + 1}`,
          label: `Field ${i + 1}`,
          width: '150px',
          type: 'text',
          sortable: true
        })
      }

      this.demoColumns = [...baseColumns, ...additionalColumns]
    },

    generateData() {
      const startTime = performance.now()
      
      const statuses = ['Active', 'Inactive', 'Pending', 'Completed', 'Cancelled']
      
      this.demoData = Array.from({ length: this.selectedDatasetSize }, (_, index) => {
        const item = {
          id: index + 1,
          name: `User ${index + 1}`,
          email: `user${index + 1}@example.com`,
          status: statuses[Math.floor(Math.random() * statuses.length)],
          amount: Math.floor(Math.random() * 10000) + 100,
          created_date: new Date(2020 + Math.random() * 4, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28))
        }

        // Add additional fields
        for (let i = 6; i < this.selectedColumnCount; i++) {
          item[`field_${i + 1}`] = `Data ${index + 1}-${i + 1}`
        }

        return item
      })

      const generationTime = performance.now() - startTime
      console.log(`Generated ${this.selectedDatasetSize} items in ${generationTime.toFixed(2)}ms`)
    },

    handlePerformanceUpdate(stats) {
      this.performanceStats = {
        ...this.performanceStats,
        ...stats,
        lastUpdate: new Date().toLocaleTimeString(),
        updateCount: this.performanceStats.updateCount + 1
      }
    },

    handleVirtualScrollUpdate(state) {
      this.virtualScrollState = { ...state }
    },

    handleScroll(scrollData) {
      this.scrollState = { ...scrollData }
    },

    handleSelectionChange(items) {
      this.selectedItems = items
    },

    getRenderTimeClass() {
      if (this.performanceStats.renderTime > 100) return 'status-error'
      if (this.performanceStats.renderTime > 50) return 'status-warning'
      return 'status-good'
    },

    getStatusClass(status) {
      const classes = {
        'Active': 'status-active',
        'Completed': 'status-completed',
        'Pending': 'status-pending',
        'Inactive': 'status-inactive',
        'Cancelled': 'status-cancelled'
      }
      return `status-badge ${classes[status] || 'status-default'}`
    },

    formatNumber(value) {
      return new Intl.NumberFormat().format(value)
    },

    formatDate(date) {
      return new Date(date).toLocaleDateString()
    },

    getSelectionPercentage() {
      if (this.demoData.length === 0) return 0
      return ((this.selectedItems.length / this.demoData.length) * 100).toFixed(1)
    }
  }
}
</script>

<style lang="scss" scoped>
.vue2-datatable-test-demo {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
  
  h1 {
    color: #374151;
    margin-bottom: 10px;
  }
  
  p {
    color: #6b7280;
    font-size: 16px;
  }
}

.controls-panel {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  flex-wrap: wrap;
  border: 1px solid #e2e8f0;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  
  label {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
  }
  
  select {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    
    &:focus {
      outline: none;
      border-color: #6366f1;
      box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    }
  }
  
  input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1);
  }
}

.performance-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  padding: 15px;
  background: #1f2937;
  color: white;
  border-radius: 8px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  
  .stat-label {
    font-size: 12px;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  .stat-value {
    font-size: 16px;
    font-weight: 600;
    
    &.status-good { color: #10b981; }
    &.status-warning { color: #f59e0b; }
    &.status-error { color: #ef4444; }
    &.status-active { color: #10b981; }
    &.status-inactive { color: #6b7280; }
  }
}

.debug-info {
  margin-top: 30px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  
  h3 {
    margin-bottom: 15px;
    color: #374151;
  }
}

.debug-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.debug-section {
  background: white;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  
  h4 {
    margin-bottom: 10px;
    color: #6366f1;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
  
  div {
    margin-bottom: 5px;
    font-size: 13px;
    color: #4b5563;
    font-family: monospace;
  }
}

// Status badges for custom slots
:deep(.status-badge) {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  
  &.status-active { background: #d1fae5; color: #065f46; }
  &.status-completed { background: #dbeafe; color: #1e40af; }
  &.status-pending { background: #fef3c7; color: #92400e; }
  &.status-inactive { background: #f3f4f6; color: #374151; }
  &.status-cancelled { background: #fee2e2; color: #991b1b; }
  &.status-default { background: #e5e7eb; color: #4b5563; }
}

:deep(.amount-cell) {
  font-weight: 600;
  color: #059669;
  font-family: monospace;
}

:deep(.date-cell) {
  font-family: monospace;
  color: #6b7280;
}
</style>
